{"name": "GoChat", "version": "2.0.1", "description": "This chat by mobail Host", "main": "server.js", "scripts": {"start": "pm2 start server.js", "stop": "pm2 stop server.js", "restart": "pm2 restart server.js"}, "dependencies": {"base64id": "^2.0.0", "bluebird": "^3.7.2", "body-parser": "^1.20.0", "bowser": "^2.11.0", "child_process": "^1.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "disable-devtool": "^0.3.4", "ejs": "^3.1.8", "express": "^4.17.1", "express-useragent": "^1.0.15", "fingerprintjs2": "^2.1.4", "fluent-ffmpeg": "^2.1.2", "fluent-ffprobe": "^1.5.1", "formidable": "^1.2.2", "g": "^2.0.1", "get-video-duration": "^4.1.0", "http": "", "ifvisible": "^1.1.0", "jimp": "^0.16.1", "multer": "^1.4.2", "mysql": "^2.18.1", "mysql2": "^2.3.3", "mysqldump": "^3.2.0", "node-fetch": "^2.6.1", "password-hash": "^1.2.2", "rate-limiter-flexible": "^2.3.6", "readline": "^1.3.0", "request": "^2.88.2", "rimraf": "^3.0.2", "sharp": "^0.32.6", "socket.io": "^4.7.2", "stop": "^3.1.0"}, "engines": {"node": "13.10.1"}, "devDependencies": {"gulp": "^3.9.1", "sw-precache": "^3.2.0"}}